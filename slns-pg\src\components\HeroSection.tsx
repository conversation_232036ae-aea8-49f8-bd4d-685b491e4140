'use client'

import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { <PERSON>Right, Star, Users, Shield } from 'lucide-react'

export default function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-accent-50"></div>
      <div className="absolute inset-0 bg-hero-pattern opacity-30"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl animate-float"></div>
      <div className="absolute top-40 right-20 w-32 h-32 bg-accent/10 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-success/10 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-10 animate-fade-in">
            <div className="space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-primary font-medium text-sm border border-primary/20">
                <Star className="h-4 w-4 mr-2" />
                Premium Accommodation Experience
              </div>
              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                Welcome to{' '}
                <span className="text-gradient bg-gradient-to-r from-primary via-primary-600 to-accent bg-clip-text text-transparent">
                  SLNS PG
                </span>
              </h1>
              <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
                Your home away from home. Experience comfortable living with modern amenities,
                nutritious food, and a secure environment designed for students and professionals.
              </p>
            </div>

            {/* Features */}
            <div className="flex flex-wrap gap-8">
              <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl shadow-elegant border border-white/20">
                <div className="p-2 bg-yellow-100 rounded-xl">
                  <Star className="h-5 w-5 text-yellow-600" />
                </div>
                <span className="text-gray-800 font-medium">Premium Facilities</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl shadow-elegant border border-white/20">
                <div className="p-2 bg-green-100 rounded-xl">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <span className="text-gray-800 font-medium">Community Living</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm px-4 py-3 rounded-2xl shadow-elegant border border-white/20">
                <div className="p-2 bg-blue-100 rounded-xl">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <span className="text-gray-800 font-medium">24/7 Security</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/register">
                <Button size="lg" className="w-full sm:w-auto">
                  Book Your Room
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="/rooms">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  View Rooms
                </Button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50+</div>
                <div className="text-sm text-gray-600">Happy Residents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">4.8</div>
                <div className="text-sm text-gray-600">Rating</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-sm text-gray-600">Support</div>
              </div>
            </div>
          </div>

          {/* Image/Video Placeholder */}
          <div className="relative">
            <div className="aspect-video bg-gradient-to-br from-primary/20 to-primary/40 rounded-2xl flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                  <div className="w-0 h-0 border-l-[12px] border-l-transparent border-b-[20px] border-b-white border-r-[12px] border-r-transparent ml-1"></div>
                </div>
                <p className="text-white font-medium">Watch Virtual Tour</p>
              </div>
            </div>
            
            {/* Floating Cards */}
            <div className="absolute -top-4 -right-4 bg-white p-4 rounded-lg shadow-lg">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium">Available Rooms</span>
              </div>
            </div>
            
            <div className="absolute -bottom-4 -left-4 bg-white p-4 rounded-lg shadow-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-primary">₹6,000</div>
                <div className="text-xs text-gray-600">Starting from</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
