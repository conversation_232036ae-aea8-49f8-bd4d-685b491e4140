@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium Color Palette */
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    /* Primary: Elegant Blue */
    --primary: 220 100% 50%;
    --primary-foreground: 210 40% 98%;

    /* Secondary: Warm Gray */
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    /* Accent: Vibrant Orange */
    --accent: 24 95% 53%;
    --accent-foreground: 60 9.1% 97.8%;

    /* Muted: Soft Gray */
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    /* Destructive: Modern Red */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    /* Success: Fresh Green */
    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    /* Warning: Bright Yellow */
    --warning: 32 95% 44%;
    --warning-foreground: 210 20% 98%;

    /* Borders and Inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 100% 50%;
    --radius: 0.75rem;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(220 100% 50%) 0%, hsl(240 100% 60%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(24 95% 53%) 0%, hsl(45 95% 58%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(220 100% 97%) 0%, hsl(240 100% 98%) 50%, hsl(260 100% 98%) 100%);
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 220 100% 60%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 24 95% 53%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 220 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  /* Custom shadows */
  .shadow-elegant {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-luxury {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply -translate-y-2 shadow-luxury;
  }

  /* Animated gradients */
  .animated-gradient {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
  }

  /* Pulse animation */
  .pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-accent to-orange-500 bg-clip-text text-transparent;
  }
}
